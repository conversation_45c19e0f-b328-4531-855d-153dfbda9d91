# Go Clean Architecture Demo

A Go application demonstrating clean architecture principles with a simple user management system and hello world endpoints.

## Project Structure

```
├── cmd/
│   └── api/                    # Application entry points
│       └── main.go            # Main application with database
├── internal/
│   ├── domain/                # Business logic layer
│   │   ├── entity/           # Domain entities
│   │   ├── repository/       # Repository interfaces
│   │   └── usecase/          # Business use cases
│   ├── infrastructure/       # External concerns
│   │   ├── database/         # Database implementations
│   │   ├── web/              # HTTP server setup
│   │   └── config/           # Configuration management
│   └── application/          # Application layer
│       ├── handler/          # HTTP handlers
│       ├── middleware/       # HTTP middleware
│       └── service/          # Application services
├── pkg/                      # Public packages
├── api/                      # API definitions
├── configs/                  # Configuration files
├── docs/                     # Documentation
├── scripts/                  # Build and deployment scripts
├── test/                     # Test files
└── main.go                   # Simple hello world server
```

## Quick Start

### Simple Hello World Server

Run the simple hello world server:

```bash
go run main.go
```

Visit http://localhost:8080 to see the hello world message.

### Full Application with Database

1. Install dependencies:
```bash
go mod tidy
```

2. Set up PostgreSQL database (optional - will show errors without DB):
```bash
# Using Docker
docker run --name postgres-test -e POSTGRES_PASSWORD=password -e POSTGRES_DB=testdb -p 5432:5432 -d postgres:15

# Or set environment variables for your existing database
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=postgres
export DB_PASSWORD=password
export DB_NAME=testdb
export DB_SSL_MODE=disable
```

3. Run the full application:
```bash
go run cmd/api/main.go
```

## API Endpoints

- `GET /hello` - Hello world JSON response
- `GET /users` - List users (with pagination)
- `POST /users` - Create a new user
- `GET /users/{id}` - Get user by ID

### Example Usage

```bash
# Hello world
curl http://localhost:8080/hello

# Create a user
curl -X POST http://localhost:8080/users \
  -H "Content-Type: application/json" \
  -d '{"name": "John Doe", "email": "<EMAIL>"}'

# List users
curl http://localhost:8080/users

# Get user by ID
curl http://localhost:8080/users/1
```

## Environment Variables

- `SERVER_PORT` - Server port (default: 8080)
- `DB_HOST` - Database host (default: localhost)
- `DB_PORT` - Database port (default: 5432)
- `DB_USER` - Database user (default: postgres)
- `DB_PASSWORD` - Database password
- `DB_NAME` - Database name (default: testdb)
- `DB_SSL_MODE` - SSL mode (default: disable)

## Clean Architecture Layers

### Domain Layer (`internal/domain/`)
- **Entities**: Core business objects (User)
- **Repositories**: Data access interfaces
- **Use Cases**: Business logic implementation

### Infrastructure Layer (`internal/infrastructure/`)
- **Database**: Repository implementations
- **Web**: HTTP server setup
- **Config**: Configuration management

### Application Layer (`internal/application/`)
- **Handlers**: HTTP request/response handling
- **Middleware**: Cross-cutting concerns (CORS, logging)
- **Services**: Application orchestration

## Testing

```bash
# Run tests
go test ./...

# Run tests with coverage
go test -cover ./...
```

## Building

```bash
# Build the application
go build -o bin/app cmd/api/main.go

# Run the built application
./bin/app
```

## Dependencies

- [Gorilla Mux](https://github.com/gorilla/mux) - HTTP router
- [lib/pq](https://github.com/lib/pq) - PostgreSQL driver
