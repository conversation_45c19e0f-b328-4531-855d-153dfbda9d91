package web

import (
	"log"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Server interface {
	Start(port int) error
	GetApp() *fiber.App
}

type App struct {
	app *fiber.App
}

func NewApp() *App {
	return &App{
		app: fiber.New(fiber.Config{
			CaseSensitive: true,
			ServerHeader:  "Fiber",
			AppName:       "Test App v0.0.1",
		}),
	}
}

func (a *App) Start(port int) error {
	portStr := ":" + strconv.Itoa(port)
	log.Printf("Starting server on port %d", port)
	return a.app.Listen(portStr)
}

func (a *App) GetApp() *fiber.App {
	return a.app
}
