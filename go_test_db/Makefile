# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=app
BINARY_UNIX=$(<PERSON>INARY_NAME)_unix

# Build targets
.PHONY: all build clean test coverage deps run run-simple help

all: test build

build:
	$(GOBUILD) -o bin/$(BINARY_NAME) cmd/api/main.go

build-linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o bin/$(BINARY_UNIX) cmd/api/main.go

clean:
	$(GOCLEAN)
	rm -f bin/$(BINARY_NAME)
	rm -f bin/$(BINARY_UNIX)

test:
	$(GOTEST) -v ./...

coverage:
	$(GOTEST) -cover ./...

deps:
	$(GOMOD) download
	$(GOMOD) tidy

run:
	$(GOCMD) run cmd/api/main.go

run-simple:
	$(GOCMD) run main.go

docker-build:
	docker build -t $(<PERSON><PERSON><PERSON>Y_NAME) .

help:
	@echo "Available targets:"
	@echo "  build        - Build the application"
	@echo "  build-linux  - Build for Linux"
	@echo "  clean        - Clean build artifacts"
	@echo "  test         - Run tests"
	@echo "  coverage     - Run tests with coverage"
	@echo "  deps         - Download and tidy dependencies"
	@echo "  run          - Run the full application"
	@echo "  run-simple   - Run the simple hello world server"
	@echo "  docker-build - Build Docker image"
	@echo "  help         - Show this help message"
